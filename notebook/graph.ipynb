{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "\n", "from typing import Annotated\n", "from typing_extensions import TypedDict\n", "\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.graph.message import add_messages\n", "\n", "from langchain_openai import ChatOpenAI\n", "from IPython.display import Image, display\n", "\n", "\n", "class State(TypedDict):\n", "    messages: Annotated[list, add_messages]\n", "\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0)\n", "\n", "\n", "def chatbot(state: State):\n", "    return {\"messages\": [llm.invoke(state[\"messages\"])]}\n", "\n", "\n", "graph_builder = StateGraph(State)\n", "graph_builder.add_node(\"chatbot\", chatbot)\n", "graph_builder.add_edge(START, \"chatbot\")\n", "graph = graph_builder.compile()\n", "\n", "try:\n", "    # 获取图的Mermaid PNG表示\n", "    mermaid_png = graph.get_graph().draw_mermaid_png()\n", "    # 使用IPython的display函数显示图像\n", "    display(Image(mermaid_png))\n", "except Exception as e:\n", "    print(e)\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}