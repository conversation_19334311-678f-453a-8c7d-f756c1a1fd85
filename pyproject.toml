[project]
name = "chat-bot"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "langgraph>=0.4.8",
    "langsmith>=0.3.45",
    "langchain-openai>=0.1.0",
    "ipython>=9.3.0",
    "ipykernel>=6.29.5",
    "python-dotenv>=1.1.0",
    "loguru>=0.7.3",
    "langchain-community>=0.3.25",
]
[[tool.poetry.source]]
name = "aliyun"
url = "https://mirrors.aliyun.com/pypi/simple/"